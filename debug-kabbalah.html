<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Kabbalah</title>
</head>
<body>
    <h1>Debug Kabbalah Ciphers</h1>
    <div>
        <label for="testWord">Test Word:</label>
        <input type="text" id="testWord" value="hello" />
        <button onclick="debugKabbalah()">Debug</button>
    </div>
    <div id="results"></div>

    <script src="ciphers.js"></script>
    <script src="Referal/oldnewgematrinator-alektryon.github.io/oldnewgematrinator-alektryon.github.io/calc/gematriaNGG.js"></script>
    <script>
        function debugKabbalah() {
            const testWord = document.getElementById('testWord').value || 'hello';
            const resultsDiv = document.getElementById('results');
            
            // Initialize reference system
            Gem_Launch();
            
            let html = `<h2>Kabbalah Debug for "${testWord}":</h2>`;
            
            const cipherMappings = [
                ['alwKabbalah', 'English Qaballa'],
                ['kfwKabbalah', 'Cipher X'],
                ['lchKabbalah', 'Trigrammaton Qabalah']
            ];
            
            cipherMappings.forEach(([yourKey, refName]) => {
                // Your implementation
                let yourTotal = 0;
                let yourBreakdown = '';
                for (let i = 0; i < testWord.length; i++) {
                    const letter = testWord[i].toLowerCase();
                    const value = ciphers[yourKey].values[letter] || 0;
                    yourTotal += value;
                    yourBreakdown += letter + '=' + value + ' ';
                }
                
                // Reference implementation
                const refCipher = allCiphers.find(c => c.Nickname === refName);
                const refTotal = refCipher ? refCipher.Gematria(testWord, 1) : 'N/A';
                
                html += `<h3>${refName}:</h3>`;
                html += `<p><strong>Your Implementation:</strong> ${yourTotal}</p>`;
                html += `<p><strong>Your Breakdown:</strong> ${yourBreakdown}</p>`;
                html += `<p><strong>Reference Implementation:</strong> ${refTotal}</p>`;
                html += `<p><strong>Match:</strong> ${yourTotal === refTotal ? 'YES' : 'NO'}</p>`;
                html += '<hr>';
            });
            
            resultsDiv.innerHTML = html;
        }

        // Test on page load
        window.onload = function() {
            debugKabbalah();
        };
    </script>
</body>
</html>
