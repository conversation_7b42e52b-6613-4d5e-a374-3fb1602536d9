<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Final Validation Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        table { border-collapse: collapse; width: 100%; margin: 20px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .match { background-color: #d4edda; }
        .mismatch { background-color: #f8d7da; }
        .summary { background-color: #e7f3ff; padding: 15px; margin: 20px 0; border-radius: 5px; }
        .test-input { margin: 20px 0; }
        .test-input input, .test-input button { padding: 8px; margin: 5px; }
    </style>
</head>
<body>
    <h1>Final Validation Test - Gematria Calculator</h1>
    
    <div class="test-input">
        <button onclick="runFullValidation()">Run Full Validation Test</button>
        <button onclick="testCustomWord()">Test Custom Word</button>
        <input type="text" id="customWord" placeholder="Enter word to test" />
    </div>
    
    <div id="summary"></div>
    <div id="results"></div>

    <script src="ciphers.js"></script>
    <script src="Referal/oldnewgematrinator-alektryon.github.io/oldnewgematrinator-alektryon.github.io/calc/gematriaNGG.js"></script>
    <script>
        function processTextWithCipher(text, cipher) {
            const words = text.toLowerCase().split(/\s+/);
            let totalValue = 0;

            words.forEach(word => {
                let wordValue = 0;
                for (let i = 0; i < word.length; i++) {
                    const letter = word[i];
                    const value = cipher.values[letter] || 0;
                    if (value) {
                        wordValue += value;
                    }
                }
                totalValue += wordValue;
            });

            return totalValue;
        }

        function testWord(word) {
            // Initialize reference system
            Gem_Launch();
            
            const cipherMappings = [
                ['englishOrdinal', 'Ordinal'],
                ['fullReduction', 'Reduction'],
                ['singleReduction', 'Single Reduction'],
                ['fullReductionKV', 'KV Exception'],
                ['singleReductionKV', 'SKV Exception'],
                ['alwKabbalah', 'English Qaballa'],
                ['kfwKabbalah', 'Cipher X'],
                ['lchKabbalah', 'Trigrammaton Qabalah'],
                ['septenary', 'Septenary'],
                ['fibonacci', 'Fibonacci'],
                ['chaldean', 'Chaldean'],
                ['keypad', 'Keypad'],
                ['satanic', 'Satanic Gematria'],
                ['reverseOrdinal', 'Reverse'],
                ['reverseFullReduction', 'Reverse Reduction'],
                ['englishSumerian', 'Sumerian'],
                ['primes', 'Primes'],
                ['trigonal', 'Trigonal'],
                ['squares', 'Squares']
            ];
            
            const results = [];
            let totalTests = 0;
            let passedTests = 0;
            
            cipherMappings.forEach(([yourKey, refName]) => {
                let yourValue = 'N/A';
                let refValue = 'N/A';
                let status = 'Unknown';
                
                // Get your implementation value
                if (ciphers[yourKey]) {
                    yourValue = processTextWithCipher(word, ciphers[yourKey]);
                    totalTests++;
                }
                
                // Get reference implementation value
                const refCipher = allCiphers.find(c => c.Nickname === refName);
                if (refCipher) {
                    refValue = refCipher.Gematria(word, 1);
                }
                
                // Compare values
                if (yourValue !== 'N/A' && refValue !== 'N/A') {
                    status = yourValue === refValue ? 'MATCH' : 'MISMATCH';
                    if (status === 'MATCH') passedTests++;
                }
                
                results.push({
                    cipher: refName,
                    yourValue,
                    refValue,
                    status
                });
            });
            
            return { results, totalTests, passedTests, word };
        }

        function runFullValidation() {
            const testWords = ['hello', 'world', 'test', 'gematria', 'cipher', 'validation', 'calculator', 'accurate'];
            const summaryDiv = document.getElementById('summary');
            const resultsDiv = document.getElementById('results');
            
            let overallTotal = 0;
            let overallPassed = 0;
            let html = '<h2>Full Validation Results:</h2>';
            
            testWords.forEach(word => {
                const testResult = testWord(word);
                overallTotal += testResult.totalTests;
                overallPassed += testResult.passedTests;
                
                html += `<h3>Testing "${word}" (${testResult.passedTests}/${testResult.totalTests} passed):</h3>`;
                html += '<table><tr><th>Cipher</th><th>Your Implementation</th><th>Reference Implementation</th><th>Status</th></tr>';
                
                testResult.results.forEach(result => {
                    const rowClass = result.status === 'MATCH' ? 'match' : (result.status === 'MISMATCH' ? 'mismatch' : '');
                    html += `<tr class="${rowClass}"><td>${result.cipher}</td><td>${result.yourValue}</td><td>${result.refValue}</td><td>${result.status}</td></tr>`;
                });
                
                html += '</table>';
            });
            
            const accuracy = ((overallPassed / overallTotal) * 100).toFixed(1);
            summaryDiv.innerHTML = `
                <div class="summary">
                    <h2>Validation Summary</h2>
                    <p><strong>Overall Accuracy: ${accuracy}%</strong></p>
                    <p>Passed Tests: ${overallPassed} / ${overallTotal}</p>
                    <p>Words Tested: ${testWords.length}</p>
                    <p>Status: ${accuracy >= 95 ? '✅ EXCELLENT' : accuracy >= 85 ? '⚠️ GOOD' : '❌ NEEDS IMPROVEMENT'}</p>
                </div>
            `;
            
            resultsDiv.innerHTML = html;
        }

        function testCustomWord() {
            const word = document.getElementById('customWord').value.trim();
            if (!word) {
                alert('Please enter a word to test');
                return;
            }
            
            const testResult = testWord(word);
            const summaryDiv = document.getElementById('summary');
            const resultsDiv = document.getElementById('results');
            
            const accuracy = ((testResult.passedTests / testResult.totalTests) * 100).toFixed(1);
            summaryDiv.innerHTML = `
                <div class="summary">
                    <h2>Test Results for "${word}"</h2>
                    <p><strong>Accuracy: ${accuracy}%</strong></p>
                    <p>Passed Tests: ${testResult.passedTests} / ${testResult.totalTests}</p>
                </div>
            `;
            
            let html = `<h2>Results for "${word}":</h2>`;
            html += '<table><tr><th>Cipher</th><th>Your Implementation</th><th>Reference Implementation</th><th>Status</th></tr>';
            
            testResult.results.forEach(result => {
                const rowClass = result.status === 'MATCH' ? 'match' : (result.status === 'MISMATCH' ? 'mismatch' : '');
                html += `<tr class="${rowClass}"><td>${result.cipher}</td><td>${result.yourValue}</td><td>${result.refValue}</td><td>${result.status}</td></tr>`;
            });
            
            html += '</table>';
            resultsDiv.innerHTML = html;
        }

        // Run validation on page load
        window.onload = function() {
            runFullValidation();
        };
    </script>
</body>
</html>
