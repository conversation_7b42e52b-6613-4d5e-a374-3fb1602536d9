<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug <PERSON>bonacci</title>
</head>
<body>
    <h1>Debug Fibonacci Cipher</h1>
    <div>
        <label for="testWord">Test Word:</label>
        <input type="text" id="testWord" value="hello" />
        <button onclick="debug<PERSON><PERSON><PERSON><PERSON>()">Debug</button>
    </div>
    <div id="results"></div>

    <script src="ciphers.js"></script>
    <script src="Referal/oldnewgematrinator-alektryon.github.io/oldnewgematrinator-alektryon.github.io/calc/gematriaNGG.js"></script>
    <script>
        function debugFibonacci() {
            const testWord = document.getElementById('testWord').value || 'hello';
            const resultsDiv = document.getElementById('results');
            
            // Initialize reference system
            Gem_Launch();
            
            let html = `<h2>Fibonacci Debug for "${testWord}":</h2>`;
            
            // Your implementation
            let yourTotal = 0;
            let yourBreakdown = '';
            for (let i = 0; i < testWord.length; i++) {
                const letter = testWord[i].toLowerCase();
                const value = ciphers.fibonacci.values[letter] || 0;
                yourTotal += value;
                yourBreakdown += letter + '=' + value + ' ';
            }
            
            // Reference implementation
            const refCipher = allCiphers.find(c => c.Nickname === 'Fibonacci');
            const refTotal = refCipher ? refCipher.Gematria(testWord, 1) : 'N/A';
            
            html += `<p><strong>Your Implementation:</strong> ${yourTotal}</p>`;
            html += `<p><strong>Your Breakdown:</strong> ${yourBreakdown}</p>`;
            html += `<p><strong>Reference Implementation:</strong> ${refTotal}</p>`;
            html += `<p><strong>Match:</strong> ${yourTotal === refTotal ? 'YES' : 'NO'}</p>`;
            
            // Show the values for each letter
            html += '<h3>Fibonacci Values:</h3>';
            html += '<table border="1"><tr><th>Letter</th><th>Your Value</th></tr>';
            for (let letter of 'abcdefghijklmnopqrstuvwxyz') {
                const value = ciphers.fibonacci.values[letter] || 0;
                html += `<tr><td>${letter}</td><td>${value}</td></tr>`;
            }
            html += '</table>';
            
            resultsDiv.innerHTML = html;
        }

        // Test on page load
        window.onload = function() {
            debugFibonacci();
        };
    </script>
</body>
</html>
