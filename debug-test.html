<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Cipher Test</title>
    <style>
        table { border-collapse: collapse; width: 100%; margin: 20px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .match { background-color: #d4edda; }
        .mismatch { background-color: #f8d7da; }
    </style>
</head>
<body>
    <h1>Debug Cipher Test</h1>
    <div>
        <label for="testWord">Test Word:</label>
        <input type="text" id="testWord" value="hello" />
        <button onclick="debugTest()">Debug Test</button>
    </div>
    <div id="results"></div>

    <script src="ciphers.js"></script>
    <script src="Referal/oldnewgematrinator-alektryon.github.io/oldnewgematrinator-alektryon.github.io/calc/gematriaNGG.js"></script>
    <script>
        function debugTest() {
            const testWord = document.getElementById('testWord').value || 'hello';
            const resultsDiv = document.getElementById('results');
            
            // Initialize reference system
            Gem_Launch();
            
            let html = `<h2>Debug Results for "${testWord}":</h2>`;
            
            // Test specific ciphers with letter breakdown
            const testCiphers = [
                ['englishOrdinal', 'Ordinal'],
                ['fullReduction', 'Reduction'],
                ['singleReduction', 'Single Reduction'],
                ['fullReductionKV', 'KV Exception'],
                ['singleReductionKV', 'SKV Exception']
            ];
            
            testCiphers.forEach(([yourKey, refName]) => {
                html += `<h3>${refName}:</h3>`;
                html += '<table><tr><th>Letter</th><th>Your Value</th><th>Reference Value</th><th>Status</th></tr>';
                
                let yourTotal = 0;
                let refTotal = 0;
                
                // Get reference cipher
                const refCipher = allCiphers.find(c => c.Nickname === refName);
                
                for (let i = 0; i < testWord.length; i++) {
                    const letter = testWord[i].toLowerCase();
                    
                    // Your implementation
                    let yourValue = 'N/A';
                    if (ciphers[yourKey] && ciphers[yourKey].values[letter]) {
                        yourValue = ciphers[yourKey].values[letter];
                        yourTotal += yourValue;
                    }
                    
                    // Reference implementation - get single letter value
                    let refValue = 'N/A';
                    if (refCipher) {
                        refValue = refCipher.Gematria(letter, 1);
                        refTotal += refValue;
                    }
                    
                    const status = yourValue === refValue ? 'MATCH' : 'MISMATCH';
                    const rowClass = status === 'MATCH' ? 'match' : 'mismatch';
                    
                    html += `<tr class="${rowClass}"><td>${letter}</td><td>${yourValue}</td><td>${refValue}</td><td>${status}</td></tr>`;
                }
                
                // Total row
                const totalStatus = yourTotal === refTotal ? 'MATCH' : 'MISMATCH';
                const totalClass = totalStatus === 'MATCH' ? 'match' : 'mismatch';
                html += `<tr class="${totalClass}"><td><strong>TOTAL</strong></td><td><strong>${yourTotal}</strong></td><td><strong>${refTotal}</strong></td><td><strong>${totalStatus}</strong></td></tr>`;
                
                html += '</table>';
            });
            
            resultsDiv.innerHTML = html;
        }

        // Test on page load
        window.onload = function() {
            debugTest();
        };
    </script>
</body>
</html>
