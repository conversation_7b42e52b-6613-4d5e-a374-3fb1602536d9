body {
	background-color: rgb(34,34,34);
	/* background-image: -webkit-linear-gradient(180deg, hsl(120 30% 8% / 0.95), hsl(180 50% 8% / 0.95)), url(./merkaba-tile.png); */
	/* background-image: -o-linear-gradient(180deg, hsl(120 30% 8% / 0.95), hsl(180 50% 8% / 0.95)), url(./merkaba-tile.png); */
	/* background-image: -moz-linear-gradient(180deg, hsl(120 30% 8% / 0.95), hsl(180 50% 8% / 0.95)), url(./merkaba-tile.png); */
	/* background-image: linear-gradient(180deg, hsl(120 30% 8% / 0.95), hsl(180 50% 8% / 0.95)), url(./merkaba-tile.png); */
	/* background-image: linear-gradient(180deg, hsl(120 0% 7% / 0.95), hsl(180 0% 7% / 0.95)), url(./flower-of-life-tile.png); */
	/* background: rgb(30,30,30); */
	background-attachment: fixed; /* no scroll */
	/* font-family: "Bookman Old Style", Arial; */
	font-family: Arial, sans-serif;
	color: rgb(222,222,222);
	/* replace cursor */
	/* cursor: url("cursor.png"), auto; */
}

.CategoryList, .CategoryList2, #OptionsTable {
	/* background-image: linear-gradient(180deg, hsl(120 30% 15% / 0.5), hsl(180 50% 8% / 1)), url(./flower-of-life-tile.png); */
}

.checkboxCipherFont {
	/* font-weight: bold; */
	/* font-family: Verdana, sans-serif */
}

.cipherCategoryActive {
	color: hsl(60 100% 50% / 1);
	display: inline-block; /* change the display type */
	margin: 5px 0; /* apply the needed vertical margins */
}

.cipherCategory {
	display: inline-block; /* change the display type */
	margin: 5px 0; /* apply the needed vertical margins */
}

#CipherChart a:link {color: hsl(187 100% 69% / 1)}
#CipherChart a:visited {color: hsl(187 100% 69% / 1)}
#CipherChart a:hover {color: hsl(180 100% 80% / 1)}
#CipherChart a:active {color: hsl(60 100% 50% / 1)}

.MenuLink a:link {color: hsl(187 100% 69% / 1)}
.MenuLink a:visited {color: hsl(187 100% 69% / 1)}
.MenuLink a:hover {color: hsl(180 100% 80% / 1)}
.MenuLink a:active {color: hsl(60 100% 50% / 1)}

.MenuLink2 a:link {color: hsl(127 100% 69% / 1)}
.MenuLink2 a:visited {color: hsl(127 100% 69% / 1)}
.MenuLink2 a:hover {color: hsl(120 100% 80% / 1)}
.MenuLink2 a:active {color: hsl(30 100% 50% / 1)}

.dragOver {
	border: 2px solid hsl(187 100% 69% / 1) !important;
}

#btn-clear-active-filter {
	left: 5px;
    bottom: 2px;
    position: relative;
	color: rgb(222,222,222);
	height: 25px;
	background-color: rgb(16,16,16);
	font-weight: bold;
}
/* ---------------- cipher table ------------------ */

#Gematria_Table {
	padding-top: 5px;
}

#GemTable {
	background-color: rgba(16,16,16,0.5);
	/* background-image: linear-gradient(180deg, hsl(120 30% 15% / 0.9), hsl(180 20% 10% / 1)), url(./flower-of-life-tile.png); */
	border: 2px solid;
	border-color: rgb(77,77,77);
	border-radius: 20px;
	padding: 5px;
}
#GemTable td {
	min-width: 125px;
	max-width: 125px;
	text-align: center;
	padding-right: 5px;
	padding-left: 5px;
}
#GemTable2 {
	background-color: rgb(16,16,16);
	border: 2px solid;
	border-color: rgb(77,77,77);
	padding: 5px;
}
#GemTable2 td {
	min-width: 50px;
	max-width: 125px;
	text-align: center;
	padding-right: 5px;
	padding-left: 5px;
}

/* ---------------- text selection ---------------- */
/*::selection {
  color: rgb(0,0,0);
  background: rgb(190,190,190);
}*/

/* ---------------- scrollbar style --------------- */

body {
  overflow: overlay;
}

::-webkit-scrollbar {
  width: 5px;
  height: 5px;
}

::-webkit-scrollbar-thumb {
  background: rgba(128, 128, 128, 0.5);
  border-radius: 20px;
}

::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.2);
}

::-webkit-scrollbar-corner
{
	background: rgba(0, 0, 0, 0.2);
}

/* ------------------------------------------------ */

#Highlight {
	background-color: rgb(255,255,255);
	color: rgb(0,0,0);
/*	font-weight: bold;	*/
	border-style: solid;
	border-width: 2px;
	border-radius: 4px;
	border-color: rgb(77,77,77);
}
#Highlight:focus{
	outline: none !important;
	border:2px solid rgb(192,192,192);
}

#SearchField {
	background-color: rgb(255,255,255);
	color: rgb(0,0,0);
/*	font-weight: bold;	*/
	border-style: solid;
	border-width: 2px;
	border-radius: 4px;
	border-color: rgb(77,77,77);
}
#SearchField:focus {
	outline: none !important;
	border:2px solid rgb(192,192,192);
}

.highlightValueBlink{
	/* background-color: rgb(255,255,0) !important; */
	animation: blink 500ms linear infinite;
}

.highlightCipherTable{
	background: rgba(192,192,192);
	color: rgb(16,16,16) !important;
}

@keyframes blink {
	0% {
		opacity: 0.0;
	}
	25% {
		opacity: 1.0;
	}
	75% {
		opacity: 1.0;
	}
	100% {
		opacity: 0.0;
	}
}

#MenuSpot {
	display: inline;
}
#MainTable {
	display: inline;
}

#canv {
	margin: 0;
	padding: 0;
	position: fixed;
	touch-action: none;
	width: 100%;
	height: 100%;
	/* for borders */
	/* width: calc(100% - 2px); */
	/* height: calc(100% - 2px); */
	/* border: 1px dashed; */
	top:0;
	left:0;
	z-index : -1; /* behind all elements */
}
		
.MenuLink {
	padding-top: 10px;
	padding-bottom: 2px;
	display: inline;
	font-size: 108%;
	font-weight: bold;
}

.MenuLink2 {
	padding-top: 10px;
	padding-bottom: 2px;
	display: inline;
	font-size: 108%;
	font-weight: bold;
}

.GemHead a {
	color: inherit !important;
	text-decoration: inherit !important;
}
.GemHead {
	padding-top: 8px;
	vertical-align: bottom;
	/* font-weight: bold; */
	/* font-family: Verdana, sans-serif */
}
.GemHead:hover {
	text-decoration: underline;
}

.GemVal {
	font-size: 200%;
	font-weight: 500;
}

.GemVal a {
	color: inherit !important;
	text-decoration: inherit !important;
}

.GemVal2 {
	font-size: 200%;
	font-weight: 500;
	padding: 12px;
}
.GemVal2 a {
	color: inherit !important;
	text-decoration: inherit !important;
}

#MatchButton {
	font-size: 90%;
	padding-left: 4px;
	padding-right: 4px;
	padding-top: 1px;
	padding-bottom: 1px;
	color: rgb(45,255,45);
	background-color: rgb(0,0,4);
}
.CipherButton {
	font-size: 75%;
	color: rgb(0,200,255);
	width: 100px;
	background-color: rgb(0,0,4);
	font-weight: normal;
}
#ChartTable {
	border-collapse: collapse;
	background: rgb(21,21,21);
	/* dynamically built based on cipher color */
	/* background: -webkit-linear-gradient(-68deg,#ac32e4,#4801ff); */
	/* background: -o-linear-gradient(-68deg,#ac32e4,#4801ff); */
	/* background: -moz-linear-gradient(-68deg,#ac32e4,#4801ff); */
	/* background: linear-gradient(0deg,#0b730045,#00000080); */
}
#ChartTable td {
	text-align: center;
	border: 1px solid;
	/* background: rgb(21,21,21); */
	border-color: rgb(77,77,77);
	padding-right: 8px;
	padding-left: 8px;
	padding-top: 5px;
	padding-bottom: 5px;
	min-width: 12px;
}
#ChartTableThin {
	border-collapse: collapse;
	background: rgb(21,21,21);
}
#ChartTableThin td {
	text-align: center;
	border: 1px solid;
	/* background: rgb(21,21,21); */
	border-color: rgb(77,77,77);
	padding-right: 4px;
	padding-left: 4px;
	padding-top: 5px;
	padding-bottom: 5px;
	min-width: 10px;
}
.ChartChar {
	color: rgb(242,153,96);
	font-size: 140%;
}
.ChartVal {
	font-size: 120%;
	font-weight: 300;
}
#BreakdownSpot {
	min-height: 50px;
	width: 800px;
	text-align: center;
	padding-top: 15px;
}

.BreakTable {
	border-collapse: collapse;
}
.BreakTable td {
	text-align: center;
	border: 1px solid;
	border-color: rgb(20,20,20);
	min-width: 20px;
}
.BreakChar {
	text-align: center;
	font-size: 114%;
	color: orange;
	background: rgb(30,30,30);
	padding-right: 2px;
	padding-left: 2px;
}
.BreakChar2 {
	text-align: center;
	font-size: 114%;
	color: orange;
	background: RGB(30,30,30);
	padding-right: 2px;
	padding-left: 2px;
}
.CipherEnd {
	font-size: 90%;
	text-align: center;
	background: RGB(21,21,21);
	padding-top: 2px;
	padding-bottom: 2px;
}
.BreakWordSum {
	font-size: 115%;
	background: rgb(25,25,25);
	padding-right: 5px;
	padding-left: 5px;
}
.BreakPhraseSum {
	font-weight: bold;
	background: rgb(21,21,21);
	font-size: 175%;
	padding-left: 10px;
	padding-right: 10px;
}
.BreakVal {
	color: rgb(211,211,211);
	background: rgb(25,25,25);
	font-size: 95%;
	padding-right: 2px;
	padding-left: 2px;
}

.breakPhrase {
	display: inline;
	font-size: 130%;
	color: rgb(222,222,222);
	font-weight: 400;
}
.breakSum {
	display: inline;
	font-size: 150%;
	color: rgb(222,222,222);
	font-weight: 900;
}
.breakCipher {
	display: inline;
	font-size: 110%;
	font-weight: 100;
}
#SimpleBreak {
	padding-bottom: 6px;
}
.LetterCounts {
	padding-bottom: 10px;
	color: rgb(244,244,196);
	font-size: 90%;
}

.HistoryTable tr, .HistoryTable td {
	border: 1px solid rgb(34,34,34);
}
.HistoryTable {
	text-align: center;
	border-spacing: 0px;
	/* border: 1px solid rgb(151,151,151); */
	border: 1px solid rgb(77,77,77);
}
.HistoryTable td {
	padding-right: 3px; padding-left: 4px;
}
.HistoryTable tr:nth-child(even) {
	background-color: rgba(25,25,25,1);
}
.HistoryTable tr:nth-child(odd) {
	background-color: rgba(19,19,19,1);
}
.historyPhrase {
	text-align: right;
}
.historyHead {
	min-width: 80px;
	max-width: 87px;
	font-size: 85%;
	padding: 5px;
	text-align: center;
}
.CipherHead {
	min-width: 125px;
	max-width: 125px;
	font-size: 85%;
	padding: 5px;
	vertical-align: bottom;
}
.ClassicEQ {
	display: inline;
	color: rgb(223,223,223);
	font-size: 90%;
}
#OptionsTable {
	/* border: 1px solid rgb(147,147,147); */
	border: 2px solid rgb(77,77,77);
	border-radius: 10px;
}

#OptionsTable td {
	text-align: right;
	padding-left: 20px;
	padding-right: 20px;
	padding-bottom: 4px;
	padding-top: 4px;
}
	
#CipherChart {
	min-width: 500px;
}
#CipherChart td {
	text-align: left;
	vertical-align: top;
	padding-left: 10px;
	padding-right: 10px;
	padding-top: 8px;
	padding-bottom: 8px;
	/* border: 1px solid rgb(147,147,147); */
	border: 2px solid rgb(77,77,77);
	border-radius: 10px;
}

.CategoryList {
	width: 150px;
	font-size: 110%;
}
.CategoryList2 {
	font-size: 110%;
}

.BottomDweller {
	float: top;
	float: right;
	font-weight: bold;
}
#SaveOptions {
	display: inline;
	font-size: 75%;
	color: rgb(255,255,148);
	width: 115px;
	background-color: rgb(0,0,8);
	float: left;
}
#SaveMsg {
	display: inline;
	font-size: 75%;
	color: rgb(255,255,148);
	float: left;
}
.CipherButton {
	display: inline-block;
	font-size: 75%;
	color: rgb(222,222,222);
	width: 95px;
	background-color: rgb(16,16,16);
}
#btn-print-cipher-png, #btn-print-history-png, #btn-save-history-png, #btn-print-word-break-png {
	display: inline-block;
	color: rgb(222,222,222);
	width: 150px;
	background-color: rgb(16,16,16);
}
.ButtonSection {float: right;}
.hideValue {display: none;}

#overlay {
	position: fixed; /* Sit on top of the page content */
	display: none; /* Hidden by default */
	width: 100%; /* Full width (cover the whole page) */
	height: 100%; /* Full height (cover the whole page) */
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: rgba(0,0,0,0.5); /* Black background with opacity */
	z-index: 1; /* Specify a stack order in case you're using a different order for other elements */
	cursor: pointer; /* Add a pointer on hover */
}

#highlight_panel {
	background-color: #101010;
	/* background-image: url("./merkaba-tile.png"); */
	border: 2px solid RGB(77,77,77);
	padding: 0;
	width: 600px;
	overflow: hidden;
	position: relative;
	top: 15%;
	left: calc(50% - 50px);
	margin: 0 0 0 -250px;
	cursor: default;
	border-radius: 6px;
	z-index: 2;
	padding-bottom: 20px;
	min-height: 366px;
	text-align: center;
}
