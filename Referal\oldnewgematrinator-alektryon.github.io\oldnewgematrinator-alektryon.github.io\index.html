<html>
<head>
<!-- Google tag (gtag.js) -->
<script async src="https://www.googletagmanager.com/gtag/js?id=G-SD1C75PP8N"></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());

  gtag('config', 'G-SD1C75PP8N');
</script>
<Title>Old New Gematrinator</Title>

<script src="lib/jquery.min.js"></script>
<script src="lib/html2canvas.min.js"></script>

<script src="calc/gematriaNGG.js"></script>
<script src="calc/ijavaNGG.js"></script>
<script src="calc/highlighter.js"></script>
<script src="calc/hltboxpanel.js"></script>
<script src="calc/history.js"></script>

</head>



<link rel="stylesheet" type="text/css" href="calc/styles.css">

<body onload="Page_Launch()">
<canvas id="canv"></canvas>

<div id="overlay"></div>
<div id="highlight_panel" style="display: none;"></div>

<!-- screenshot, html2canvas -->
<center><div id="screenshotToolbar"></div></center><p>
	
<center>
	
<div id="MenuSpot"></div><br>

<div id="MainTable"><table><tr>
	<td><center>
		<h3>Enter word, phrase or number:</h3>
	</center></td>

	</tr><tr>

	<td><center><input id="SearchField" autofocus type="text" enterkeyhint="done" oninput="FieldChange(sVal())" style="width: 500px; min-width: 500px; font-size: 125%" onkeydown="navHistory(event.keyCode) "ondrop="dropHandler(event)" ondragover="dragOverHandler(event)"></input>
	<right><input id="Highlight" type="text" enterkeyhint="done" style="width: 200px; min-width: 200px; font-size: 125%"></input><span id="clearFilterButton"></span></right>

	</center><P>

	<div id="Gematria_Table"></div>
	<div id="BreakdownSpot"></div>

	</td>
</tr></table></div>

	<P>
	<center><div id="ChartSpot" style="min-height: 182px;"></div></center><P>
	<div id="MiscSpot"></div>
	
</center>
</body>



<!-- add after page elements are created -->
<script src="calc/coderain.js"></script>
<script src="calc/screenshot.js"></script>
</HTML>
