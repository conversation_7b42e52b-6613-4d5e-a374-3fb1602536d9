<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reference Cipher Test</title>
    <style>
        table { border-collapse: collapse; width: 100%; margin: 20px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
    </style>
</head>
<body>
    <h1>Reference Cipher Test</h1>
    <div>
        <label for="testWord">Test Word:</label>
        <input type="text" id="testWord" value="hello" />
        <button onclick="testReference()">Test Reference</button>
    </div>
    <div id="results"></div>

    <script src="Referal/oldnewgematrinator-alektryon.github.io/oldnewgematrinator-alektryon.github.io/calc/gematriaNGG.js"></script>
    <script>
        function testReference() {
            const testWord = document.getElementById('testWord').value || 'hello';
            const resultsDiv = document.getElementById('results');
            
            // Initialize reference system
            Gem_Launch();
            
            let html = `<h2>Reference Results for "${testWord}":</h2>`;
            html += '<table><tr><th>Cipher Name</th><th>Value</th></tr>';
            
            // Test all the cipher names from your table
            const cipherNames = [
                'Ordinal',
                'Reduction',
                'Single Reduction',
                'KV Exception',
                'SKV Exception',
                'Standard',
                'Capitals Added',
                'Capitals Mixed',
                'Satanic Gematria',
                'Reverse',
                'Reverse Reduction',
                'Reverse Single Reduction',
                'EP Exception',
                'EHP Exception',
                'Reverse Standard',
                'Reverse Caps Added',
                'Reverse Caps Mixed',
                'Reverse Satanic',
                'Latin',
                'Latin Ordinal',
                'Latin Reduction',
                'English Qaballa',
                'Cipher X',
                'Trigrammaton Qabalah',
                'Sumerian',
                'Reverse Sumerian',
                'Primes',
                'Trigonal',
                'Squares',
                'Reverse Primes',
                'Reverse Trigonal',
                'Reverse Squares',
                'Fibonacci',
                'Septenary',
                'Chaldean',
                'Keypad',
                'Alphanumeric Qabbala'
            ];
            
            cipherNames.forEach(cipherName => {
                const cipher = allCiphers.find(c => c.Nickname === cipherName);
                if (cipher) {
                    const value = cipher.Gematria(testWord, 1);
                    html += `<tr><td>${cipherName}</td><td>${value}</td></tr>`;
                } else {
                    html += `<tr><td>${cipherName}</td><td>NOT FOUND</td></tr>`;
                }
            });
            
            html += '</table>';
            resultsDiv.innerHTML = html;
        }

        // Test on page load
        window.onload = function() {
            testReference();
        };
    </script>
</body>
</html>
