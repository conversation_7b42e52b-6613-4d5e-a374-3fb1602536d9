<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cipher Comparison Test</title>
    <style>
        table { border-collapse: collapse; width: 100%; margin: 20px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .match { background-color: #d4edda; }
        .mismatch { background-color: #f8d7da; }
        .test-input { margin: 20px 0; }
        .test-input input { padding: 5px; margin: 5px; }
        .test-input button { padding: 5px 10px; }
    </style>
</head>
<body>
    <h1>Cipher Comparison Test</h1>
    <div class="test-input">
        <label for="testWord">Test Word:</label>
        <input type="text" id="testWord" value="hello" />
        <button onclick="runComparison()">Compare Single Word</button>
        <button onclick="testMultipleWords()">Test Multiple Words</button>
    </div>
    <div id="results"></div>

    <script src="ciphers.js"></script>
    <script src="Referal/oldnewgematrinator-alektryon.github.io/oldnewgematrinator-alektryon.github.io/calc/gematriaNGG.js"></script>
    <script>
        // Your implementation
        function processTextWithCipher(text, cipher) {
            const words = text.toLowerCase().split(/\s+/);
            let totalValue = 0;

            words.forEach(word => {
                let wordValue = 0;
                for (let i = 0; i < word.length; i++) {
                    const letter = word[i];
                    const value = cipher.values[letter] || 0;
                    if (value) {
                        wordValue += value;
                    }
                }
                totalValue += wordValue;
            });

            return totalValue;
        }

        function runComparison() {
            const testWord = document.getElementById('testWord').value || 'hello';
            const resultsDiv = document.getElementById('results');
            
            // Initialize reference system
            Gem_Launch();
            
            let html = `<h2>Comparison Results for "${testWord}":</h2>`;
            html += `<table><tr><th>Cipher</th><th>Your Implementation</th><th>Reference Implementation</th><th>Status</th></tr>`;
            
            // Cipher mappings: [your cipher key, reference cipher name]
            const cipherMappings = [
                ['englishOrdinal', 'Ordinal'],
                ['fullReduction', 'Reduction'],
                ['singleReduction', 'Single Reduction'],
                ['fullReductionKV', 'KV Exception'],
                ['singleReductionKV', 'SKV Exception'],
                ['alwKabbalah', 'English Qaballa'],
                ['kfwKabbalah', 'Cipher X'],
                ['lchKabbalah', 'Trigrammaton Qabalah'],
                ['septenary', 'Septenary'],
                ['fibonacci', 'Fibonacci'],
                ['chaldean', 'Chaldean'],
                ['keypad', 'Keypad'],
                ['satanic', 'Satanic Gematria'],
                ['reverseOrdinal', 'Reverse'],
                ['reverseFullReduction', 'Reverse Reduction'],
                ['englishSumerian', 'Sumerian'],
                ['primes', 'Primes'],
                ['trigonal', 'Trigonal'],
                ['squares', 'Squares']
            ];
            
            cipherMappings.forEach(([yourKey, refName]) => {
                let yourValue = 'N/A';
                let refValue = 'N/A';
                let status = 'Unknown';
                
                // Get your implementation value
                if (ciphers[yourKey]) {
                    yourValue = processTextWithCipher(testWord, ciphers[yourKey]);
                }
                
                // Get reference implementation value
                const refCipher = allCiphers.find(c => c.Nickname === refName);
                if (refCipher) {
                    refValue = refCipher.Gematria(testWord, 1);
                }
                
                // Compare values
                if (yourValue !== 'N/A' && refValue !== 'N/A') {
                    status = yourValue === refValue ? 'MATCH' : 'MISMATCH';
                }
                
                const rowClass = status === 'MATCH' ? 'match' : (status === 'MISMATCH' ? 'mismatch' : '');
                html += `<tr class="${rowClass}"><td>${refName}</td><td>${yourValue}</td><td>${refValue}</td><td>${status}</td></tr>`;
            });
            
            html += '</table>';
            resultsDiv.innerHTML = html;
        }

        // Test multiple words
        function testMultipleWords() {
            const testWords = ['hello', 'world', 'test', 'gematria', 'cipher'];
            const resultsDiv = document.getElementById('results');
            
            // Initialize reference system
            Gem_Launch();
            
            let html = '<h2>Multiple Word Test Results:</h2>';
            
            testWords.forEach(testWord => {
                html += `<h3>Testing "${testWord}":</h3>`;
                html += `<table><tr><th>Cipher</th><th>Your Implementation</th><th>Reference Implementation</th><th>Status</th></tr>`;
                
                const cipherMappings = [
                    ['englishOrdinal', 'Ordinal'],
                    ['fullReduction', 'Reduction'],
                    ['singleReduction', 'Single Reduction'],
                    ['fullReductionKV', 'KV Exception'],
                    ['singleReductionKV', 'SKV Exception'],
                    ['alwKabbalah', 'English Qaballa'],
                    ['kfwKabbalah', 'Cipher X'],
                    ['lchKabbalah', 'Trigrammaton Qabalah'],
                    ['septenary', 'Septenary'],
                    ['fibonacci', 'Fibonacci']
                ];
                
                cipherMappings.forEach(([yourKey, refName]) => {
                    let yourValue = 'N/A';
                    let refValue = 'N/A';
                    let status = 'Unknown';
                    
                    if (ciphers[yourKey]) {
                        yourValue = processTextWithCipher(testWord, ciphers[yourKey]);
                    }
                    
                    const refCipher = allCiphers.find(c => c.Nickname === refName);
                    if (refCipher) {
                        refValue = refCipher.Gematria(testWord, 1);
                    }
                    
                    if (yourValue !== 'N/A' && refValue !== 'N/A') {
                        status = yourValue === refValue ? 'MATCH' : 'MISMATCH';
                    }
                    
                    const rowClass = status === 'MATCH' ? 'match' : (status === 'MISMATCH' ? 'mismatch' : '');
                    html += `<tr class="${rowClass}"><td>${refName}</td><td>${yourValue}</td><td>${refValue}</td><td>${status}</td></tr>`;
                });
                
                html += '</table>';
            });
            
            resultsDiv.innerHTML = html;
        }

        // Test on page load
        window.onload = function() {
            runComparison();
        };
    </script>
</body>
</html>
